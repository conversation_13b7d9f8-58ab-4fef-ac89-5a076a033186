// T3Config.h
#pragma once

#include <string>

namespace T3Config {
    // t3网络验证配置
    static const std::string Host = "http://w.t3yanzheng.com"; // t3网络验证线路地址
    static const std::string AppKey = "0de73a064560f18b03d7da911ceb2e59"; // t3网络验证程序应用密钥
    static const std::string RsaPublicKey = "-----BEGIN PUBLIC KEY-----\n"
                                            "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDj9Z6eSHXGr05DvnSYsxMsLNo7\n"
                                            "3yLswlI5gunyP3wDWifMzwTal3rySSrZU+t1Se19eq9zyJl9akE9LJBD/5M3mB8q\n"
                                            "Xcgih4VkTlvg5+Ff81B2fHm54oVVVCiRu7RhnuCnFl0L00ulAX4oBFAZksyg16Zw\n"
                                            "Z4/sC4bFZU+HZldPnwIDAQAB\n"
                                            "-----END PUBLIC KEY-----";
    // API路径
    static const std::string Path_SingleLogin = "777028A1B8094CFF"; // t3网络验证程序单码卡密登录API调用路径
    static const std::string Path_IsSingleLoginStatus = "1715FBD6C2B6B627"; // t3网络验证程序单码卡密登录状态查询API调用路径
    static const std::string Path_GetProgramNotice = "97D15E87CF662CC5"; // t3网络验证程序获取公告API调用路径
    static const std::string Path_GetProgramVersionNumber = "CBC8373B9A88C85C"; // t3网络验证程序获取版本号API调用路径
    static const std::string Path_GetValueContent = "FEE8B8AB889A75D2"; // t3网络验证程序获取变量内容API调用路径
}